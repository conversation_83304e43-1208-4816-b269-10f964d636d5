# SecPaid Shopify Payment Gateway

A simple Shopify payment app that integrates with SecPaid's payment processing service.

## Features

- **Simple Integration**: Create payment links and handle callbacks
- **Offsite Payments**: Customers are redirected to SecPaid's secure payment page
- **Test Mode Support**: Full testing capabilities with SecPaid's development environment
- **Automatic Callbacks**: Handle payment success/cancellation automatically
- **Manual Refunds**: Refunds are processed manually in SecPaid dashboard (as per SecPaid's simple model)

## How It Works

1. **Customer Checkout**: Customer selects SecPaid as payment method in Shopify checkout
2. **Payment Link Creation**: App creates a SecPaid payment link with order details
3. **Redirect**: Customer is redirected to SecPaid's payment page
4. **Payment Processing**: Customer completes payment on SecPaid
5. **Callback**: SecPaid sends callback to app with payment status
6. **Order Completion**: App notifies Shopify and customer returns to store

## Setup Instructions

### 1. Prerequisites

- Node.js 18+
- A Shopify Partner account
- A SecPaid account with API access
- ngrok or similar tunneling service for development

### 2. Installation

```bash
# Clone the repository
git clone <repository-url>
cd secpaid-shopify-app

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your actual values

# Set up database
npm run setup
```

### 3. Shopify App Configuration

1. Create a new app in your Shopify Partner Dashboard
2. Set the app URL to your tunnel URL (e.g., `https://abc123.ngrok.io`)
3. Set the redirect URL to `https://abc123.ngrok.io/auth/callback`
4. Copy the API key and secret to your `.env` file

### 4. SecPaid Configuration

1. Log into your SecPaid dashboard
2. Generate an API token
3. Set your callback URL to: `https://abc123.ngrok.io/app/secpaid_callback`
4. Note your API token for the app configuration

### 5. Development

```bash
# Start the development server
npm run dev

# The app will be available at your tunnel URL
# Install it on a development store to test
```

### 6. Configuration

1. Install the app on your development store
2. Open the app and enter your SecPaid API token
3. Enable test mode for development
4. Test with a sample order

## File Structure

```
├── app/
│   ├── routes/
│   │   ├── app._index.jsx          # Configuration page
│   │   ├── app.payment_session.jsx # Handle Shopify payment requests
│   │   ├── app.secpaid_callback.jsx # Handle SecPaid callbacks
│   │   └── app.refund_session.jsx  # Handle refund requests (rejects with manual instruction)
│   ├── lib/
│   │   └── secpaid.js              # SecPaid API client
│   └── shopify.server.js           # Shopify app configuration
├── extensions/
│   └── secpaid-payment/
│       └── shopify.extension.toml  # Payment extension configuration
├── prisma/
│   └── schema.prisma               # Database schema
└── package.json
```

## API Endpoints

- `POST /app/payment_session` - Receives payment requests from Shopify
- `GET /app/secpaid_callback` - Receives callbacks from SecPaid
- `POST /app/refund_session` - Handles refund requests (rejects with manual instruction)

## Environment Variables

```bash
SHOPIFY_API_KEY=your_shopify_api_key
SHOPIFY_API_SECRET=your_shopify_api_secret
SCOPES=write_payment_gateways,write_payment_sessions
SHOPIFY_APP_URL=https://your-tunnel-url.ngrok.io
DATABASE_URL="file:./dev.db"
```

## Testing

1. Enable test mode in the app configuration
2. Create a test product in your development store
3. Go through checkout and select SecPaid
4. Complete payment on SecPaid's test environment
5. Verify order is marked as paid in Shopify

## Production Deployment

1. Deploy to your hosting platform (Heroku, Railway, etc.)
2. Update environment variables with production values
3. Update SecPaid callback URL to production domain
4. Disable test mode in app configuration
5. Submit app for Shopify review (if distributing publicly)

## Important Notes

- **Refunds**: Must be processed manually in SecPaid dashboard
- **Test Mode**: Always test thoroughly before going live
- **Callbacks**: Ensure your callback URL is accessible and secure
- **API Token**: Keep your SecPaid API token secure

## Troubleshooting

### Payment Not Completing
- Check SecPaid callback URL is correct
- Verify API token is valid
- Check app logs for errors

### Callback Issues
- Ensure callback URL is publicly accessible
- Check for HTTPS requirement
- Verify callback parameters are being received

### Configuration Problems
- Verify all environment variables are set
- Check Shopify app permissions
- Ensure database is properly initialized

## Support

For SecPaid-specific issues, contact SecPaid support.
For Shopify integration issues, refer to Shopify's payment app documentation.
