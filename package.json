{"name": "secpaid-shopify-app", "version": "1.0.0", "description": "SecPaid payment integration for Shopify", "type": "module", "scripts": {"build": "remix build", "dev": "shopify app dev", "config:link": "shopify app config link", "config:use": "shopify app config use", "deploy": "shopify app deploy", "docker-start": "npm run setup && npm run start", "setup": "prisma generate && prisma migrate deploy", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc --noEmit", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "prisma": "prisma"}, "dependencies": {"@prisma/client": "^5.0.0", "@remix-run/node": "^2.0.0", "@remix-run/react": "^2.0.0", "@remix-run/serve": "^2.0.0", "@shopify/polaris": "^12.0.0", "@shopify/shopify-app-remix": "^3.0.0", "@shopify/shopify-app-session-storage-prisma": "^5.0.0", "isbot": "^3.6.8", "prisma": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "remix": "^2.0.0"}, "devDependencies": {"@remix-run/dev": "^2.0.0", "@shopify/app": "^3.80.0", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "eslint": "^8.42.0", "typescript": "^5.1.3"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "engines": {"node": ">=18.0.0"}}