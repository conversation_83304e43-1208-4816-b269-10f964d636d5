{"name": "secpaid-shopify-app", "version": "1.0.0", "description": "SecPaid payment integration for Shopify", "type": "module", "scripts": {"build": "remix build", "dev": "shopify app dev", "deploy": "shopify app deploy", "start": "remix-serve ./build/server/index.js", "setup": "prisma generate && prisma migrate deploy"}, "dependencies": {"@prisma/client": "^5.0.0", "@remix-run/node": "^2.0.0", "@remix-run/react": "^2.0.0", "@remix-run/serve": "^2.0.0", "@shopify/polaris": "^12.0.0", "@shopify/shopify-app-remix": "^3.0.0", "@shopify/shopify-app-session-storage-prisma": "^5.0.0", "prisma": "^5.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@remix-run/dev": "^2.0.0", "@shopify/app": "^3.80.0", "@types/react": "^18.2.6", "typescript": "^5.1.3"}, "workspaces": ["extensions/*"]}