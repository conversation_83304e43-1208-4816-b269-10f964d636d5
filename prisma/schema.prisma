// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Session {
  id          String    @id
  shop        String
  state       String
  isOnline    Bo<PERSON>an   @default(false)
  scope       String?
  expires     DateTime?
  accessToken String
  userId      BigInt?
  firstName   String?
  lastName    String?
  email       String?
  accountOwner <PERSON><PERSON>an  @default(false)
  locale      String?
  collaborator <PERSON><PERSON><PERSON>? @default(false)
  emailVerified Boolean? @default(false)
}

model PaymentSession {
  id                String   @id
  shopifyPaymentId  String   @unique
  secpaidPaymentId  String?
  secpaidLinkId     String?
  amount            String
  currency          String
  status            String   @default("pending") // pending, processing, completed, failed, cancelled
  shopDomain        String
  orderId           String?
  customerEmail     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  @@map("payment_sessions")
}

model AppConfig {
  id              Int     @id @default(autoincrement())
  shop            String  @unique
  secpaidApiToken String?
  isTestMode      Boolean @default(true)
  callbackUrl     String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  @@map("app_configs")
}
