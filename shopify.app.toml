# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

name = "SecPaid Payment Gateway"
client_id = "YOUR_CLIENT_ID_HERE"
application_url = "https://YOUR_TUNNEL_URL_HERE"
embedded = false

[build]
automatically_update_urls_on_dev = true
dev_store_url = "YOUR_DEV_STORE_URL_HERE"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_payment_gateways,write_payment_sessions"

[auth]
redirect_urls = [
  "https://YOUR_TUNNEL_URL_HERE/auth/callback",
  "https://YOUR_TUNNEL_URL_HERE/auth/shopify/callback",
  "https://YOUR_TUNNEL_URL_HERE/api/auth/callback"
]

[webhooks]
api_version = "2024-10"

[pos]
embedded = false
