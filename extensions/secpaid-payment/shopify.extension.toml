# Learn more about configuring your extension at https://shopify.dev/docs/apps/build/payments

api_version = "2024-10"

[[extensions]]
type = "payments_app_extension"
name = "secpaid-payment"
handle = "secpaid-payment"

  [extensions.settings]
  payment_session_url = "/app/payment_session"
  refund_session_url = "/app/refund_session"
  
  # SecPaid supports these countries (you can adjust based on SecPaid's actual coverage)
  supported_countries = ["US", "CA", "GB", "DE", "FR", "ES", "IT", "NL", "BE", "AT", "CH"]
  
  # SecPaid doesn't require 3DS (adjust if needed)
  supports_3ds = false
  
  # Payment methods supported by SecPaid
  supported_payment_methods = ["visa", "master", "american_express", "discover", "paypal", "bank_transfer"]
  
  # SecPaid doesn't support installments (adjust if they do)
  supports_installments = false
  
  # SecPaid doesn't support deferred payments (adjust if they do)
  supports_deferred_payments = false
  
  # Display names
  merchant_label = "SecPaid"
  buyer_label = "SecPaid"
  
  # Enable test mode for development
  test_mode_available = true
