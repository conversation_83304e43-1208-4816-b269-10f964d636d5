/**
 * SecPaid API Client
 * Simple client for creating payment links and handling callbacks
 */

export class SecPaidClient {
  constructor(apiToken, isTestMode = true) {
    this.apiToken = apiToken;
    this.isTestMode = isTestMode;
    this.baseUrl = isTestMode 
      ? 'https://api.dev.secpaid.com' // Assuming dev API endpoint
      : 'https://api.secpaid.com';    // Assuming prod API endpoint
  }

  /**
   * Create a payment link with SecPaid
   * @param {Object} paymentData - Payment information
   * @param {string} paymentData.amount - Amount in cents
   * @param {string} paymentData.currency - Currency code (e.g., 'USD')
   * @param {string} paymentData.orderId - Shopify order ID
   * @param {string} paymentData.customerEmail - Customer email
   * @param {string} paymentData.callbackUrl - URL for SecPaid to send callbacks
   * @param {string} paymentData.cancelUrl - URL for customer to return on cancel
   * @returns {Promise<Object>} SecPaid payment link response
   */
  async createPaymentLink(paymentData) {
    try {
      const response = await fetch(`${this.baseUrl}/payment-links`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiToken}`,
        },
        body: JSON.stringify({
          amount: paymentData.amount,
          currency: paymentData.currency,
          reference: paymentData.orderId,
          customer_email: paymentData.customerEmail,
          callback_url: paymentData.callbackUrl,
          cancel_url: paymentData.cancelUrl,
          description: `Order ${paymentData.orderId}`,
        }),
      });

      if (!response.ok) {
        throw new Error(`SecPaid API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // Expected response format (adjust based on actual SecPaid API):
      // {
      //   "link_id": "encoded_link_id",
      //   "payment_url": "https://app.secpaid.com/payment?link_id=encoded_link_id",
      //   "payment_id": "secpaid_payment_id"
      // }
      
      return {
        linkId: data.link_id,
        paymentUrl: data.payment_url,
        paymentId: data.payment_id,
      };
    } catch (error) {
      console.error('SecPaid API Error:', error);
      throw error;
    }
  }

  /**
   * Get payment status from SecPaid
   * @param {string} paymentId - SecPaid payment ID
   * @returns {Promise<Object>} Payment status
   */
  async getPaymentStatus(paymentId) {
    try {
      const response = await fetch(`${this.baseUrl}/payments/${paymentId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiToken}`,
        },
      });

      if (!response.ok) {
        throw new Error(`SecPaid API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('SecPaid API Error:', error);
      throw error;
    }
  }

  /**
   * Validate callback signature (if SecPaid provides signature validation)
   * @param {Object} callbackData - Callback data from SecPaid
   * @param {string} signature - Signature header from SecPaid
   * @returns {boolean} Whether the callback is valid
   */
  validateCallback(callbackData, signature) {
    // TODO: Implement signature validation if SecPaid provides it
    // For now, we'll rely on the callback URL being secret
    return true;
  }

  /**
   * Generate the payment URL for the given environment
   * @param {string} linkId - Encoded link ID from SecPaid
   * @returns {string} Full payment URL
   */
  getPaymentUrl(linkId) {
    const baseUrl = this.isTestMode 
      ? 'https://app.dev.secpaid.com'
      : 'https://app.secpaid.com';
    
    return `${baseUrl}/payment?link_id=${linkId}`;
  }
}
