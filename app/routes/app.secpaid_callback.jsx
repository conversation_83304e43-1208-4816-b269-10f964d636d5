import { json, redirect } from "@remix-run/node";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

/**
 * Handle callbacks from SecPaid
 * SecPaid sends: ?pay_id=<pay_id>&user_id=<user_id>&status=success|cancel
 */
export async function loader({ request }) {
  const url = new URL(request.url);
  const payId = url.searchParams.get("pay_id");
  const userId = url.searchParams.get("user_id");
  const status = url.searchParams.get("status");
  const sessionId = url.searchParams.get("session_id");

  console.log("SecPaid callback received:", { payId, userId, status, sessionId });

  if (!payId || !status || !sessionId) {
    console.error("Missing required callback parameters");
    return json({ error: "Missing required parameters" }, { status: 400 });
  }

  try {
    // Find the payment session
    const paymentSession = await prisma.paymentSession.findUnique({
      where: { id: sessionId }
    });

    if (!paymentSession) {
      console.error("Payment session not found:", sessionId);
      return json({ error: "Payment session not found" }, { status: 404 });
    }

    // Update payment session status
    const newStatus = status === "success" ? "completed" : "cancelled";
    
    await prisma.paymentSession.update({
      where: { id: sessionId },
      data: {
        status: newStatus,
        updatedAt: new Date(),
      },
    });

    // Notify Shopify about the payment result
    await notifyShopify(paymentSession, status === "success");

    console.log(`Payment ${sessionId} ${newStatus}`);

    // Redirect customer back to Shopify
    // The redirect URL should be provided by Shopify in the original payment request
    // For now, we'll redirect to a generic success/cancel page
    if (status === "success") {
      return redirect(`https://${paymentSession.shopDomain}/checkout/thank_you`);
    } else {
      return redirect(`https://${paymentSession.shopDomain}/checkout`);
    }

  } catch (error) {
    console.error("Callback processing error:", error);
    return json({ error: "Failed to process callback" }, { status: 500 });
  }
}

/**
 * Notify Shopify about payment completion using GraphQL mutations
 */
async function notifyShopify(paymentSession, isSuccess) {
  try {
    // Get app configuration to access Shopify API
    const appConfig = await prisma.appConfig.findUnique({
      where: { shop: paymentSession.shopDomain }
    });

    if (!appConfig) {
      throw new Error("App configuration not found");
    }

    // Prepare GraphQL mutation
    const mutation = isSuccess ? 
      `mutation paymentSessionResolve($id: ID!) {
        paymentSessionResolve(id: $id) {
          paymentSession {
            id
            state {
              ... on PaymentSessionStateResolved {
                code
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }` :
      `mutation paymentSessionReject($id: ID!, $reason: PaymentSessionStateRejectedReason!) {
        paymentSessionReject(id: $id, reason: $reason) {
          paymentSession {
            id
            state {
              ... on PaymentSessionStateRejected {
                code
                reason
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }`;

    const variables = isSuccess ? 
      { id: paymentSession.shopifyPaymentId } :
      { 
        id: paymentSession.shopifyPaymentId, 
        reason: "PROCESSING_ERROR" // or "BUYER_CANCELLED" based on status
      };

    // Make GraphQL request to Shopify
    const response = await fetch(`https://${paymentSession.shopDomain}/payments_apps/api/2024-10/graphql.json`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": appConfig.accessToken, // We'll need to store this
      },
      body: JSON.stringify({
        query: mutation,
        variables: variables,
      }),
    });

    if (!response.ok) {
      throw new Error(`Shopify API error: ${response.status}`);
    }

    const result = await response.json();
    console.log("Shopify notification result:", result);

    if (result.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
    }

  } catch (error) {
    console.error("Failed to notify Shopify:", error);
    // Don't throw here - we still want to redirect the customer
    // But we should implement retry logic or manual reconciliation
  }
}

// Handle POST requests (in case SecPaid sends webhooks via POST)
export async function action({ request }) {
  if (request.method === "POST") {
    // Handle webhook-style callbacks
    const body = await request.json();
    console.log("SecPaid webhook received:", body);
    
    // Process webhook data similar to GET callback
    // This would depend on SecPaid's webhook format
    
    return json({ received: true });
  }
  
  return json({ error: "Method not allowed" }, { status: 405 });
}
