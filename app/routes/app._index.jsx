import { json, redirect } from "@remix-run/node";
import { useLoaderData, useActionData, Form } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  FormLayout,
  TextField,
  Button,
  Banner,
  Checkbox,
  Text,
  BlockStack,
} from "@shopify/polaris";
import { PrismaClient } from "@prisma/client";
import { authenticate } from "../shopify.server";

const prisma = new PrismaClient();

export async function loader({ request }) {
  const { session } = await authenticate.admin(request);
  
  // Get existing configuration
  const config = await prisma.appConfig.findUnique({
    where: { shop: session.shop }
  });

  return json({
    shop: session.shop,
    config: config || {
      secpaidApiToken: "",
      isTestMode: true,
      callbackUrl: "",
    }
  });
}

export async function action({ request }) {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  
  const secpaidApiToken = formData.get("secpaidApiToken");
  const isTestMode = formData.get("isTestMode") === "on";

  try {
    // Upsert configuration
    await prisma.appConfig.upsert({
      where: { shop: session.shop },
      update: {
        secpaidApiToken,
        isTestMode,
        updatedAt: new Date(),
      },
      create: {
        shop: session.shop,
        secpaidApiToken,
        isTestMode,
      },
    });

    return json({ 
      success: true, 
      message: "Configuration saved successfully!" 
    });
  } catch (error) {
    console.error("Configuration save error:", error);
    return json({ 
      success: false, 
      message: "Failed to save configuration" 
    }, { status: 500 });
  }
}

export default function Index() {
  const { shop, config } = useLoaderData();
  const actionData = useActionData();

  return (
    <Page title="SecPaid Configuration">
      <Layout>
        <Layout.Section>
          {actionData?.success && (
            <Banner status="success" title="Success">
              <p>{actionData.message}</p>
            </Banner>
          )}
          
          {actionData?.success === false && (
            <Banner status="critical" title="Error">
              <p>{actionData.message}</p>
            </Banner>
          )}

          <Card>
            <BlockStack gap="500">
              <Text variant="headingMd" as="h2">
                SecPaid Payment Gateway Configuration
              </Text>
              
              <Text variant="bodyMd" color="subdued">
                Configure your SecPaid API credentials to start accepting payments.
              </Text>

              <Form method="post">
                <FormLayout>
                  <TextField
                    label="SecPaid API Token"
                    name="secpaidApiToken"
                    type="password"
                    defaultValue={config.secpaidApiToken}
                    helpText="Enter your SecPaid API token. You can find this in your SecPaid dashboard."
                    autoComplete="off"
                  />

                  <Checkbox
                    label="Test Mode"
                    name="isTestMode"
                    defaultChecked={config.isTestMode}
                    helpText="Enable test mode to use SecPaid's development environment."
                  />

                  <Button submit primary>
                    Save Configuration
                  </Button>
                </FormLayout>
              </Form>
            </BlockStack>
          </Card>
        </Layout.Section>

        <Layout.Section secondary>
          <Card>
            <BlockStack gap="300">
              <Text variant="headingMd" as="h3">
                Setup Instructions
              </Text>
              
              <BlockStack gap="200">
                <Text variant="bodyMd">
                  <strong>1. Get your SecPaid API Token:</strong>
                </Text>
                <Text variant="bodyMd" color="subdued">
                  Log into your SecPaid dashboard and navigate to API settings to generate an API token.
                </Text>

                <Text variant="bodyMd">
                  <strong>2. Configure Callback URL:</strong>
                </Text>
                <Text variant="bodyMd" color="subdued">
                  In your SecPaid dashboard, set your callback URL to:
                  <br />
                  <code>https://your-app-domain.com/app/secpaid_callback</code>
                </Text>

                <Text variant="bodyMd">
                  <strong>3. Test the Integration:</strong>
                </Text>
                <Text variant="bodyMd" color="subdued">
                  Enable test mode and make a test purchase to verify everything works correctly.
                </Text>

                <Text variant="bodyMd">
                  <strong>4. Go Live:</strong>
                </Text>
                <Text variant="bodyMd" color="subdued">
                  Once testing is complete, disable test mode and update your callback URL to use your production domain.
                </Text>
              </BlockStack>
            </BlockStack>
          </Card>

          <Card>
            <BlockStack gap="300">
              <Text variant="headingMd" as="h3">
                Important Notes
              </Text>
              
              <BlockStack gap="200">
                <Text variant="bodyMd" color="subdued">
                  • Refunds must be processed manually in the SecPaid dashboard
                </Text>
                <Text variant="bodyMd" color="subdued">
                  • Ensure your SecPaid account supports the countries where you sell
                </Text>
                <Text variant="bodyMd" color="subdued">
                  • Test thoroughly before processing live payments
                </Text>
              </BlockStack>
            </BlockStack>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
