import { json } from "@remix-run/node";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

/**
 * Handle refund requests from Shopify
 * Since SecPaid likely doesn't support API refunds, we'll reject these
 * and instruct merchants to process refunds manually in SecPaid dashboard
 */
export async function action({ request }) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const refundRequest = await request.json();
    
    console.log("Received refund request:", refundRequest);

    const {
      id: refundId,
      gid,
      payment_id,
      amount,
      currency,
      reason,
    } = refundRequest;

    // Find the original payment session
    const paymentSession = await prisma.paymentSession.findUnique({
      where: { shopifyPaymentId: payment_id }
    });

    if (!paymentSession) {
      console.error("Original payment session not found for refund:", payment_id);
      return json({ error: "Original payment not found" }, { status: 404 });
    }

    // Since SecPaid likely doesn't support API refunds, we'll reject this
    // and provide instructions for manual processing
    
    // Immediately reject the refund with a helpful message
    await notifyShopifyRefundRejected(refundId, paymentSession.shopDomain);

    console.log(`Refund ${refundId} rejected - manual processing required`);

    // Return 201 as required by Shopify
    return json({}, { status: 201 });

  } catch (error) {
    console.error("Refund session error:", error);
    return json({ error: "Failed to process refund" }, { status: 500 });
  }
}

/**
 * Notify Shopify that the refund was rejected
 */
async function notifyShopifyRefundRejected(refundId, shopDomain) {
  try {
    const appConfig = await prisma.appConfig.findUnique({
      where: { shop: shopDomain }
    });

    if (!appConfig) {
      throw new Error("App configuration not found");
    }

    const mutation = `
      mutation refundSessionReject($id: ID!, $reason: RefundSessionRejectionReasonInput!) {
        refundSessionReject(id: $id, reason: $reason) {
          refundSession {
            id
            state {
              ... on RefundSessionStateRejected {
                code
                reason {
                  code
                  merchantMessage
                }
              }
            }
          }
          userErrors {
            field
            message
          }
        }
      }
    `;

    const variables = {
      id: refundId,
      reason: {
        code: "PROCESSING_ERROR",
        merchantMessage: "Refunds must be processed manually in the SecPaid dashboard. Please log into your SecPaid account to issue this refund."
      }
    };

    const response = await fetch(`https://${shopDomain}/payments_apps/api/2024-10/graphql.json`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": appConfig.accessToken,
      },
      body: JSON.stringify({
        query: mutation,
        variables: variables,
      }),
    });

    if (!response.ok) {
      throw new Error(`Shopify API error: ${response.status}`);
    }

    const result = await response.json();
    console.log("Shopify refund rejection result:", result);

  } catch (error) {
    console.error("Failed to notify Shopify about refund rejection:", error);
  }
}

export function loader() {
  return json({ error: "Method not allowed" }, { status: 405 });
}
