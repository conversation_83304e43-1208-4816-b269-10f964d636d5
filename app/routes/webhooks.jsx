import { authenticate } from "../shopify.server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export const action = async ({ request }) => {
  const { topic, shop, session, admin, payload } = await authenticate.webhook(request);

  if (!admin && topic !== "APP_UNINSTALLED") {
    // The admin context isn't returned if the webhook fired after a shop was uninstalled.
    throw new Response();
  }

  switch (topic) {
    case "APP_UNINSTALLED":
      // Clean up app data when app is uninstalled
      try {
        await prisma.appConfig.delete({
          where: { shop }
        });
        
        // Optionally clean up payment sessions older than 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        await prisma.paymentSession.deleteMany({
          where: {
            shopDomain: shop,
            createdAt: {
              lt: thirtyDaysAgo
            }
          }
        });
        
        console.log(`Cleaned up data for uninstalled shop: ${shop}`);
      } catch (error) {
        console.error(`Failed to clean up data for shop ${shop}:`, error);
      }
      break;
    default:
      throw new Response("Unhandled webhook topic", { status: 404 });
  }

  throw new Response();
};
