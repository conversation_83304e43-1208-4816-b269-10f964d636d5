import { json } from "@remix-run/node";
import { PrismaClient } from "@prisma/client";
import { SecPaidClient } from "../lib/secpaid.js";

const prisma = new PrismaClient();

/**
 * Handle payment session requests from Shopify
 * This is called when a customer selects SecPaid as payment method
 */
export async function action({ request }) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Parse the payment request from Shopify
    const paymentRequest = await request.json();
    
    console.log("Received payment request:", paymentRequest);

    // Extract payment details from Shopify request
    const {
      id: shopifyPaymentId,
      gid,
      group,
      amount,
      currency,
      test,
      merchant_locale,
      payment_method,
      customer,
      billing_address,
      shipping_address,
      cancel_url,
    } = paymentRequest;

    // Get shop domain from the request headers or payment data
    const shopDomain = request.headers.get("x-shopify-shop-domain") || 
                      paymentRequest.shop?.domain ||
                      "unknown-shop.myshopify.com";

    // Get app configuration for this shop
    const appConfig = await prisma.appConfig.findUnique({
      where: { shop: shopDomain }
    });

    if (!appConfig || !appConfig.secpaidApiToken) {
      console.error("SecPaid not configured for shop:", shopDomain);
      return json({ 
        error: "SecPaid not configured. Please configure your SecPaid API token." 
      }, { status: 400 });
    }

    // Initialize SecPaid client
    const secpaid = new SecPaidClient(appConfig.secpaidApiToken, appConfig.isTestMode);

    // Create callback URL for SecPaid to notify us
    const callbackUrl = `${request.headers.get("x-forwarded-proto") || "https"}://${
      request.headers.get("host")
    }/app/secpaid_callback`;

    // Create payment session record
    const paymentSession = await prisma.paymentSession.create({
      data: {
        id: crypto.randomUUID(),
        shopifyPaymentId: shopifyPaymentId,
        amount: amount.toString(),
        currency: currency,
        status: "pending",
        shopDomain: shopDomain,
        orderId: group || "unknown",
        customerEmail: customer?.email || null,
      },
    });

    // Create SecPaid payment link
    const secpaidResponse = await secpaid.createPaymentLink({
      amount: Math.round(parseFloat(amount) * 100), // Convert to cents
      currency: currency,
      orderId: paymentSession.id, // Use our internal ID as reference
      customerEmail: customer?.email,
      callbackUrl: `${callbackUrl}?session_id=${paymentSession.id}`,
      cancelUrl: cancel_url,
    });

    // Update payment session with SecPaid details
    await prisma.paymentSession.update({
      where: { id: paymentSession.id },
      data: {
        secpaidPaymentId: secpaidResponse.paymentId,
        secpaidLinkId: secpaidResponse.linkId,
        status: "processing",
      },
    });

    console.log("Created SecPaid payment link:", secpaidResponse.paymentUrl);

    // Return redirect URL to Shopify
    return json({
      redirect_url: secpaidResponse.paymentUrl,
    });

  } catch (error) {
    console.error("Payment session error:", error);
    
    return json({
      error: "Failed to create payment session",
      details: error.message,
    }, { status: 500 });
  }
}

// This route only handles POST requests, no GET needed
export function loader() {
  return json({ error: "Method not allowed" }, { status: 405 });
}
